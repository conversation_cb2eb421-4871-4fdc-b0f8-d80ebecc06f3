# 连接时间分析系统说明

## 概述

本系统为RiderService SDK实现了完整的连接时间分析功能，能够精确记录和分析以下4种关键连接时间数据：

1. **蓝牙连接时间** - 从BLE连接开始到连接成功的时间
2. **WiFi连接时间** - 从WiFi连接开始到连接成功的时间（支持AP客户端模式和P2P模式）
3. **TCP连接时间** - 从TCP连接开始到连接成功的时间
4. **总连接时间** - 从蓝牙连接开始到TCP连接成功的完整流程时间

## 系统架构

### 核心组件

#### 1. ConnectionSession (连接会话)
- **文件**: `ConnectionSession.kt`
- **功能**: 跟踪完整连接流程的数据模型
- **关键字段**:
  - `bleStartTime` / `bleConnectedTime` - 蓝牙连接时间点
  - `wifiStartTime` / `wifiConnectedTime` - WiFi连接时间点
  - `tcpStartTime` / `tcpConnectedTime` - TCP连接时间点
  - `currentStage` - 当前连接阶段
  - `wifiMode` - WiFi连接模式（AP_CLIENT/P2P）

#### 2. ConnectionSessionManager (会话管理器)
- **文件**: `ConnectionSessionManager.kt`
- **功能**: 管理连接会话的生命周期
- **主要方法**:
  - `startSession()` - 开始新会话
  - `recordBleConnected()` - 记录BLE连接成功
  - `recordWifiConnecting()` / `recordWifiConnected()` - 记录WiFi连接状态
  - `recordTcpConnecting()` / `recordTcpConnected()` - 记录TCP连接状态
  - `getConnectionTimeStats()` - 获取统计数据

#### 3. SimpleConnectionLogger (连接记录器)
- **文件**: `SimpleConnectionLogger.kt`
- **功能**: 记录连接数据到缓存文件（仅Debug模式）
- **输出文件**:
  - `connection_records.txt` - 单个连接记录
  - `connection_sessions.txt` - 完整会话记录
  - `connection_times_report.txt` - 统计报表

### 集成点

#### ConnectionManager集成
在`ConnectionManager.kt`中的关键集成点：

1. **BLE连接事件**:
   ```kotlin
   override fun onDeviceConnecting(device: BluetoothDevice) {
       // 开始新的连接会话
       currentSessionId = ConnectionSessionManager.startSession(device.address)
   }
   
   override fun onDeviceConnected(device: BluetoothDevice) {
       // 记录BLE连接成功
       currentSessionId?.let { sessionId ->
           ConnectionSessionManager.recordBleConnected(sessionId)
       }
   }
   ```

2. **WiFi连接事件**:
   ```kotlin
   override fun onWifiConnecting(ssid: String) {
       // 记录WiFi连接开始（AP模式）
       currentSessionId?.let { sessionId ->
           ConnectionSessionManager.recordWifiConnecting(sessionId, "AP_CLIENT")
       }
   }
   
   override fun onWifiConnected(ssid: String, ipAddress: String) {
       // 记录WiFi连接成功
       currentSessionId?.let { sessionId ->
           ConnectionSessionManager.recordWifiConnected(sessionId)
       }
   }
   ```

3. **TCP连接事件**:
   ```kotlin
   override fun onDeviceConnected() {
       // 记录TCP连接成功（会话完成）
       currentSessionId?.let { sessionId ->
           ConnectionSessionManager.recordTcpConnected(sessionId)
       }
   }
   ```

#### DisplayNaviManager集成
通过回调机制支持P2P模式的WiFi连接跟踪：

```kotlin
private var displayNaviManager: DisplayNaviManager = DisplayNaviManager(
    // ... 其他参数
    onP2pWifiConnected = {
        recordP2pWifiConnected()
    },
    onTcpConnecting = {
        recordTcpConnecting()
    }
)
```

## 使用方法

### 1. 获取当前会话信息
```kotlin
val sessionInfo = connectionManager.getConnectionSessionInfo()
println(sessionInfo)
```

### 2. 获取连接时间统计
```kotlin
val timeStats = ConnectionSessionManager.getConnectionTimeStats()
println("平均总连接时间: ${timeStats.averageTotalTime}ms")
```

### 3. 生成调试报表（仅Debug模式）
```kotlin
connectionManager.generateConnectionTimeReport()
```

### 4. 清除记录
```kotlin
connectionManager.clearConnectionTimeRecords()
```

## 输出示例

### 会话信息输出
```
=== 连接会话分析 ===
当前会话:
  会话ID: CS_a1b2c3d4
  设备ID: 00:11:22:33:44:55
  当前阶段: TCP_CONNECTED
  进度: 100%
  蓝牙连接时间: 1250ms
  WiFi连接时间: 2100ms
  TCP连接时间: 800ms
  总连接时间: 4150ms

历史统计 (15个会话):
  平均蓝牙连接时间: 1180ms
  平均WiFi连接时间: 2350ms
  平均TCP连接时间: 750ms
  平均总连接时间: 4280ms
  最快总连接时间: 3200ms
  最慢总连接时间: 6800ms
```

### 缓存文件格式

#### connection_sessions.txt
```
2024-01-01 10:30:15|SESSION|CS_a1b2c3d4|00:11:22:33:44:55|BLE:1250ms|WiFi:2100ms(AP_CLIENT)|TCP:800ms|TOTAL:4150ms|SUCCESS
2024-01-01 10:35:22|SESSION|CS_e5f6g7h8|00:11:22:33:44:66|BLE:1180ms|WiFi:2350ms(P2P)|TCP:750ms|TOTAL:4280ms|SUCCESS
```

## 配置选项

### Debug模式控制
系统仅在`BuildConfig.DEBUG = true`时记录详细数据到缓存文件。

### 历史记录限制
- 会话历史：最多保存500条记录
- 连接记录：最多保存1000条记录

### 文件位置
所有缓存文件保存在应用的cache目录：
- Android: `/data/data/[package]/cache/`

## 性能考虑

1. **内存使用**: 使用ConcurrentHashMap和CopyOnWriteArrayList确保线程安全
2. **文件I/O**: 采用追加模式写入，避免频繁的文件重写
3. **数据量控制**: 自动清理超出限制的历史记录
4. **Debug限制**: 详细记录仅在Debug模式下启用

## 扩展性

系统设计支持以下扩展：

1. **新的连接类型**: 可轻松添加新的ConnectionType
2. **自定义监听器**: 通过ConnectionSessionListener接口
3. **数据导出**: 可扩展支持JSON、CSV等格式
4. **实时监控**: 可集成到监控面板或调试工具

## 故障排除

### 常见问题

1. **会话数据不完整**
   - 检查是否在所有连接事件中正确调用了记录方法
   - 确认currentSessionId不为null

2. **缓存文件为空**
   - 确认应用运行在Debug模式
   - 检查应用是否有cache目录写入权限

3. **统计数据不准确**
   - 确认连接流程完整执行
   - 检查是否有异常中断导致会话未正常完成

### 调试建议

1. 启用详细日志查看连接流程
2. 使用`getConnectionSessionInfo()`实时查看会话状态
3. 检查缓存文件内容验证数据记录
